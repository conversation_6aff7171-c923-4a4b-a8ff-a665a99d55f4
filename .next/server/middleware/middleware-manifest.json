{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_1ff2e906._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_835ea4b9.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/(/?index|/?index\\\\.json)?", "originalSource": "/"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/(bs|de|en)/:path*{(\\\\.json)}?", "originalSource": "/(bs|de|en)/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "mBPkuLSKcMTGczlXPu79aNpGNxUol+OqU0kgv7T36PE=", "__NEXT_PREVIEW_MODE_ID": "401dae6f499df2572a06597ecdf771d3", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "9eb08725e459cc93333cf9217e4c194e6c1def2c287873c5a7d114bb720f68eb", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "f28a01362b40217194b703b75abfe0330aca1d82631c6cfbaafbda7494c4f28f"}}}, "instrumentation": null, "functions": {}}