{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_1ff2e906._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_835ea4b9.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/(/?index|/?index\\\\.json)?", "originalSource": "/"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/(bs|de|en)/:path*{(\\\\.json)}?", "originalSource": "/(bs|de|en)/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "mBPkuLSKcMTGczlXPu79aNpGNxUol+OqU0kgv7T36PE=", "__NEXT_PREVIEW_MODE_ID": "9be13299eb00200b12c54e1a41a1deb4", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d4b5c2258b2d6038f3d121fbcc10cc64bb3ee3b05de144294c1d51f70be40984", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ba89f10d954cbffc739abe3438be657efb5c100b546ab9dbedee2617fad685ad"}}}, "instrumentation": null, "functions": {}}