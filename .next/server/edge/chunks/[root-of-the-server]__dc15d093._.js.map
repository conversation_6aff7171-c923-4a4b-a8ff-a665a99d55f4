{"version": 3, "sources": [], "sections": [{"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import createMiddleware from 'next-intl/middleware';\n\nexport default createMiddleware({\n  // A list of all locales that are supported\n  locales: ['bs', 'en', 'de'],\n\n  // Used when no locale matches\n  defaultLocale: 'bs',\n\n  // Don't use a prefix for the default locale\n  localePrefix: {\n    mode: 'as-needed'\n  }\n});\n\nexport const config = {\n  // Match only internationalized pathnames\n  // Include /bs to handle redirects to root, and /de, /en for other locales\n  matcher: ['/', '/(bs|de|en)/:path*']\n};\n"], "names": [], "mappings": ";;;;AAAA;;uCAEe,CAAA,GAAA,8LAAA,CAAA,UAAgB,AAAD,EAAE;IAC9B,2CAA2C;IAC3C,SAAS;QAAC;QAAM;QAAM;KAAK;IAE3B,8BAA8B;IAC9B,eAAe;IAEf,4CAA4C;IAC5C,cAAc;QACZ,MAAM;IACR;AACF;AAEO,MAAM,SAAS;IACpB,yCAAyC;IACzC,0EAA0E;IAC1E,SAAS;QAAC;QAAK;KAAqB;AACtC"}}]}