{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_1ff2e906._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_835ea4b9.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(bs|de|en))(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/(bs|de|en)/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "mBPkuLSKcMTGczlXPu79aNpGNxUol+OqU0kgv7T36PE=", "__NEXT_PREVIEW_MODE_ID": "d3ad063b309150c1df121fdab3675c13", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "9163272e25602f619a3147e6c86a1122ac63192c2e9d56e04aa1949f873fe3fe", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0f87b47829ba7f9ff298168393819a213d067bcc4f0f0ae73cd1d1b2c09eee4e"}}}, "sortedMiddleware": ["/"], "functions": {}}